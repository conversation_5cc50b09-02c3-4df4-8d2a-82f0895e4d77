[Unit]
Description=MCShop Django Application with Q Cluster
After=network.target
Requires=network.target

[Service]
Type=simple
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/mcshop
ExecStart=/home/<USER>/mcshop/start-all.bash
Restart=always
RestartSec=10
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30

# Logging
StandardOutput=journal
StandardError=journal
SyslogIdentifier=mcshop-combined

[Install]
WantedBy=multi-user.target
