#!/bin/bash
set -eux
cd /home/<USER>/mcshop/
source .venv/bin/activate

# Function to handle cleanup on exit
cleanup() {
    echo "Shutting down services..."
    kill $DJANGO_PID $DJANGOQ_PID 2>/dev/null || true
    wait
    echo "Services stopped."
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Start Django web server in background
echo "Starting Django web server..."
gunicorn --workers=2 -b 0.0.0.0:4000 mcshop.wsgi &
DJANGO_PID=$!
echo "Django started with PID: $DJANGO_PID"

# Start Django-Q cluster in background
echo "Starting Django-Q cluster..."
python manage.py qcluster &
DJANGOQ_PID=$!
echo "Django-Q started with PID: $DJANGOQ_PID"

# Wait for both processes
echo "Both services started. Waiting for processes..."
echo "Django PID: $DJANGO_PID"
echo "Django-Q PID: $DJANGOQ_PID"

# Wait for either process to exit
wait $DJAN<PERSON><PERSON>_PID $DJANGOQ_PID
