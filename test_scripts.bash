#!/bin/bash

echo "Testing startup scripts..."

# Make scripts executable
chmod +x start.bash start_djangoq.bash

echo "✅ Scripts made executable"

# Test if scripts have correct syntax
echo "Testing start.bash syntax..."
bash -n start.bash && echo "✅ start.bash syntax OK" || echo "❌ start.bash syntax error"

echo "Testing start_djangoq.bash syntax..."
bash -n start_djangoq.bash && echo "✅ start_djangoq.bash syntax OK" || echo "❌ start_djangoq.bash syntax error"

echo ""
echo "Scripts are ready for systemd services!"
echo ""
echo "Next steps:"
echo "1. Run: ./setup_services.bash"
echo "2. Check: ./check_services.bash"
