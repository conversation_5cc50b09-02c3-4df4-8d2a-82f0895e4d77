#!/usr/bin/env python
"""
Simple test script for Zarinpal integration
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'mcshop.settings')
django.setup()

from shop.zarinpal import ZarinpalService
from shop.views import build_frontend_payment_result_url

def test_frontend_urls():
    """Test frontend URL generation"""
    print("Testing Frontend URL Generation...")

    # Test success URL
    success_url = build_frontend_payment_result_url(
        'success',
        ref_id='ABC123',
        message='Payment completed successfully'
    )
    print(f"Success URL: {success_url}")

    # Test failure URL
    failure_url = build_frontend_payment_result_url(
        'failed',
        ref_id='ABC123',
        message='Payment verification failed'
    )
    print(f"Failure URL: {failure_url}")

    # Test already processed URL
    processed_url = build_frontend_payment_result_url(
        'success',
        ref_id='ABC123',
        message='Payment was already processed'
    )
    print(f"Already Processed URL: {processed_url}")

def test_zarinpal_service():
    """Test the Zarinpal service"""
    print("Testing Zarinpal Service...")

    service = ZarinpalService()
    print(f"Merchant ID: {service.merchant_id}")
    print(f"Sandbox Mode: {service.sandbox}")
    print(f"Base URL: {service.base_url}")
    print(f"Payment URL: {service.payment_url}")

    # Test payment request (this will fail in sandbox without proper merchant ID)
    print("\nTesting payment request...")
    success, response = service.request_payment(
        amount=10000,  # 1000 Tomans = 10000 Rials
        description="Test payment",
        callback_url="http://localhost:8000/shop/api/ipg/callback/",
        mobile="09123456789",
        order_id="test_123"
    )

    print(f"Payment request success: {success}")
    print(f"Response: {response}")

    if success:
        authority = response.get('authority')
        payment_url = service.get_payment_url(authority)
        print(f"Payment URL: {payment_url}")

if __name__ == "__main__":
    test_frontend_urls()
    print("\n" + "="*50 + "\n")
    test_zarinpal_service()
