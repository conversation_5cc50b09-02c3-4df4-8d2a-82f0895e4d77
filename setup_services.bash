#!/bin/bash
set -e

echo "Setting up Django-Q as a system service..."

# Copy service file to systemd directory
sudo cp django-q.service /etc/systemd/system/

# Reload systemd to recognize the new service
sudo systemctl daemon-reload

# Enable the service to start on boot
sudo systemctl enable django-q

# Start the service
sudo systemctl start django-q

# Check status
sudo systemctl status django-q

echo "Django-Q service setup complete!"
echo ""
echo "Useful commands:"
echo "  sudo systemctl start django-q     # Start the service"
echo "  sudo systemctl stop django-q      # Stop the service"
echo "  sudo systemctl restart django-q   # Restart the service"
echo "  sudo systemctl status django-q    # Check service status"
echo "  sudo journalctl -u django-q -f    # View live logs"
