from .minecraft import send_command_to_minecraft_server
from .models import Purchase
from django.utils.timezone import now
from datetime import timed<PERSON><PERSON>


def revoke_item(purchase_id: int):
    try:
        purchase = Purchase.objects.get(id=purchase_id)
        if (
                purchase.subscription_status == Purchase.SubscriptionStatus.SUBSCRIBED
                and purchase.expires_at
                and purchase.expires_at <= now()
        ):
            # Check if there's a newer active subscription for the same user and item
            has_renewed = Purchase.objects.filter(
                minecraft_username=purchase.minecraft_username,
                item=purchase.item,
                subscription_status=Purchase.SubscriptionStatus.SUBSCRIBED,
                expires_at__gt=now()
            ).exclude(id=purchase.id).exists()

            # If renewed, don't revoke access — only expire this record
            if has_renewed:
                purchase.subscription_status = Purchase.SubscriptionStatus.EXPIRED
                purchase.save()
                return

            # Otherwise, revoke access
            server = purchase.item.minecraft_server
            item = purchase.item

            # Execute revoke commands if they exist
            if server and item.revoke_commands:
                revoke_commands = [cmd.strip() for cmd in item.revoke_commands.split(',') if cmd.strip()]
                command_success = True

                for command in revoke_commands:
                    command = command.replace("{username}", purchase.minecraft_username)
                    if not send_command_to_minecraft_server(server, command):
                        command_success = False
                        purchase.subscription_status = Purchase.SubscriptionStatus.FAILED_REVOKE
                        purchase.save()
                        break

                if command_success:
                    purchase.subscription_status = Purchase.SubscriptionStatus.EXPIRED
                    purchase.save()

    except Purchase.DoesNotExist:
        pass


def timeout_unpaid_purchases():
    """
    Check for purchases created longer than 10 minutes ago with state "created"
    and mark them as timeout.
    """
    timeout_threshold = now() - timedelta(minutes=10)

    # Find purchases that are still in "created" state and older than 10 minutes
    unpaid_purchases = Purchase.objects.filter(
        state=Purchase.State.CREATED,
        created_at__lt=timeout_threshold
    )

    # Update them to timeout state
    timeout_count = unpaid_purchases.update(state=Purchase.State.TIMEOUT)

    if timeout_count > 0:
        print(f"Marked {timeout_count} purchases as timeout")

    return timeout_count
