from rest_framework import serializers
from .models import Purchase, MinecraftServer, Category, ContentCreator, Item, DownloadLink


class MinecraftServerSerializer(serializers.ModelSerializer):
    class Meta:
        model = MinecraftServer
        fields = ['id', 'name', 'display_name', 'domain', 'enabled']


class CategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'display_name', 'description', 'enabled']


class ContentCreatorSerializer(serializers.ModelSerializer):
    class Meta:
        model = ContentCreator
        fields = ['id', 'name', 'display_name', 'description', 'image', 'enabled']


class ItemSerializer(serializers.ModelSerializer):
    class Meta:
        model = Item
        fields = ['id', 'name', 'display_name', 'description', 'image', 'category', 'price',
                  'ucoin_price', 'expiration_days', 'is_one_time', 'description', 'enabled']


class PurchaseSerializer(serializers.ModelSerializer):
    class Meta:
        model = Purchase
        fields = ['id', 'minecraft_username', 'mobile_number', 'item', 'referrer']


class DownloadLinkSerializer(serializers.ModelSerializer):
    class Meta:
        model = DownloadLink
        fields = ['platform', 'url', 'enabled']
