import psycopg2
from psycopg2 import sql
from psycopg2.extras import RealDictCursor
from decouple import config
from mctools import RCONClient
from .models import MinecraftServer


def send_command_to_minecraft_server(server: MinecraftServer, command: str) -> bool:
    """
    Send a command to a Minecraft server using RCON protocol.

    Args:
        server: MinecraftServer instance with ip and rcon_port
        command: The command to execute on the server

    Returns:
        bool: True if command was executed successfully, False otherwise
    """
    try:
        # Validate server has required RCON configuration
        if not server.rcon_port:
            print(f"RCON port not configured for server {server.name}")
            return False

        # Get RCON password from environment
        rcon_password = config("RCON_PASSWORD")
        if not rcon_password:
            print("RCON password not configured in environment variables")
            return False

        # Create RCON client instance
        rcon = RCONClient(server.ip, port=server.rcon_port)

        try:
            # Authenticate with the RCON server
            auth_success = rcon.login(rcon_password)
            if not auth_success:
                print(f"RCON authentication failed for server {server.name}")
                return False

            # Execute the command
            response = rcon.command(command)
            print(f"Command '{command}' executed successfully. Response: {response}")
            return True
        except Exception as e:
            print(f"RCON command execution failed for server {server.name}: {e}")
            return False

        finally:
            # Always stop the RCON connection
            rcon.stop()

    except Exception as e:
        print(f"RCON command execution failed for server {server.name}: {e}")
        return False


def is_valid_minecraft_username(username: str) -> bool:
    """
    Check if a username exists in the Minecraft server's authme database.
    First validates the username format, then checks against the database.
    """
    # Initial format validation
    # if not (username.isalnum() and 3 <= len(username) <= 16):
    #     return False

    # Get the proxy server details
    proxy_server = MinecraftServer.objects.filter(proxy=True, enabled=True).first()
    if not proxy_server:
        return False

    try:
        # Connect to the PostgreSQL database
        conn = psycopg2.connect(
            host=proxy_server.ip,
            port=config("AUTHME_DB_PORT", cast=int),
            dbname=config("AUTHME_DB_NAME"),
            user=config("AUTHME_DB_USER"),
            password=config("AUTHME_DB_PASSWORD"),
            connect_timeout=5
        )

        with conn.cursor(cursor_factory=RealDictCursor) as cursor:
            # Using a parameterized query to prevent SQL injection
            query = sql.SQL("SELECT username FROM authme WHERE LOWER(username) = LOWER(%s)")
            cursor.execute(query, (username,))
            result = cursor.fetchone()

            return result is not None

    except Exception as e:
        print(f"Database error while validating username: {e}")
        return False
    finally:
        if 'cursor' in locals():
            cursor.close()
        if 'conn' in locals():
            conn.close()
