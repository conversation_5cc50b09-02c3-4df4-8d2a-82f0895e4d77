from django.db import models
import json
import random
import string


# Create your models here.
class MinecraftServer(models.Model):
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=100, null=True, blank=True)
    ip = models.GenericIPAddressField()
    port = models.PositiveIntegerField()
    domain = models.CharField(max_length=255, null=True, blank=True)
    rcon_port = models.PositiveIntegerField(null=True, blank=True)
    query_port = models.PositiveIntegerField(null=True, blank=True)
    proxy = models.BooleanField(default=False)
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.ip}:{self.port})"


class DownloadLink(models.Model):
    class PlatformType(models.TextChoices):
        WINDOWS = "windows"
        ANDROID = "android"
        MAC = "mac"
        Linux = "linux"

    platform = models.Char<PERSON>ield(max_length=20, choices=PlatformType.choices, unique=True)
    url = models.URLField(max_length=500)
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.get_platform_display()}"


class Category(models.Model):
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=150, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)

    def __str__(self):
        return self.name


class ContentCreator(models.Model):
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=150, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(upload_to='creators/', null=True, blank=True)
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)

    def __str__(self):
        return self.name


class Item(models.Model):
    name = models.CharField(max_length=100)
    display_name = models.CharField(max_length=150, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    image = models.ImageField(upload_to='items/', null=True, blank=True)
    category = models.ForeignKey(Category, on_delete=models.CASCADE)
    price = models.IntegerField(default=0)
    ucoin_price = models.PositiveIntegerField(default=0)
    commands = models.TextField(blank=True, null=True, help_text="Use ',' to separate multiple commands")
    revoke_commands = models.TextField(blank=True, null=True, help_text="Use ',' to separate multiple revoke commands")
    minecraft_server = models.ForeignKey(MinecraftServer, on_delete=models.SET_NULL, null=True, blank=True)
    expiration_days = models.PositiveIntegerField(null=True, blank=True)
    enabled = models.BooleanField(default=True)
    published = models.BooleanField(default=True)

    def __str__(self):
        return self.name


    @property
    def is_one_time(self):
        return self.expiration_days == 0


class Purchase(models.Model):
    class State(models.TextChoices):
        CREATED = "created"
        SUCCESSFUL = "successful"
        FAILED = "failed"
        COMMAND_FAILED = "command_failed"
        FINISHED = "finished"

    class SubscriptionStatus(models.TextChoices):
        ONETIME = 'onetime'
        SUBSCRIBED = 'subscribed'
        EXPIRED = 'expired'
        FAILED_REVOKE = 'failed_revoke'

    minecraft_username = models.CharField(max_length=100)
    mobile_number = models.CharField(max_length=15, blank=True, null=True)
    item = models.ForeignKey(Item, on_delete=models.CASCADE)
    state = models.CharField(max_length=20, choices=State.choices, default=State.CREATED)
    referrer = models.ForeignKey(ContentCreator, null=True, blank=True, on_delete=models.SET_NULL)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField(null=True, blank=True)
    payment_succeeded_at = models.DateTimeField(null=True, blank=True)
    subscription_status = models.CharField(
        max_length=20,
        choices=SubscriptionStatus.choices,
        default=SubscriptionStatus.ONETIME
    )
    ref_id = models.CharField(max_length=8, unique=True, blank=True, null=True, help_text="8-digit human-readable reference ID")
    # Zarinpal fields
    authority = models.CharField(max_length=100, blank=True, null=True, help_text="Zarinpal authority code")
    zarinpal_ref_id = models.CharField(max_length=100, blank=True, null=True, help_text="Zarinpal reference ID")
    zarinpal_verify_response = models.TextField(blank=True, null=True, help_text="Complete Zarinpal verify response as JSON")
    zarinpal_code = models.IntegerField(blank=True, null=True, help_text="Zarinpal verification code (100, 101, etc.)")

    def __str__(self):
        return f"{self.minecraft_username} - {self.item.name} [{self.state}]"

    @classmethod
    def generate_reference_id(cls):
        """Generate a unique 8-digit human-readable reference ID"""
        # Use characters that are easy to read and distinguish (avoid 0, O, 1, I)
        # chars = '23456789ABCDEFGHJKLMNPQRSTUVWXYZ'
        chars = '1234567890'

        while True:
            # Generate 8-character reference ID
            reference_id = ''.join(random.choices(chars, k=8))

            # Check if it's unique
            if not cls.objects.filter(ref_id=reference_id).exists():
                return reference_id

    def save_zarinpal_verify_response(self, response_data):
        """Save Zarinpal verify response and extract code"""
        if response_data:
            self.zarinpal_verify_response = json.dumps(response_data)
            self.zarinpal_code = response_data.get('code')
            self.save()

    def save(self, *args, **kwargs):
        # Generate reference_id if not set
        if not self.ref_id:
            self.ref_id = self.generate_reference_id()
        super().save(*args, **kwargs)
