from datetime import timedelta
from urllib.parse import urlencode

from django.http import HttpResponseRedirect
from django.utils import timezone
from django_q.tasks import schedule
from mcstatus import JavaServer
from rest_framework import status
from rest_framework.response import Response
from rest_framework.views import APIView

from .minecraft import send_command_to_minecraft_server, is_valid_minecraft_username
from .models import Purchase, Item, Category, ContentCreator, MinecraftServer, DownloadLink
from .serializers import (
    PurchaseSerializer, ItemSerializer, CategorySerializer,
    ContentCreatorSerializer, DownloadLinkSerializer
)
from .zarinpal import request_payment, get_payment_url, verify_payment


def build_frontend_payment_result_url(payment_status, ref_id=None, message=None):
    """Helper function to build frontend payment result URL with proper encoding"""
    from django.conf import settings

    params = {'status': payment_status}
    if ref_id:
        params['refId'] = ref_id
    if message:
        params['message'] = message

    query_string = urlencode(params)
    return f"{settings.FRONTEND_BASE_URL}/shop/payment-result?{query_string}"


class PurchaseView(APIView):
    def post(self, request):
        serializer = PurchaseSerializer(data=request.data)
        if serializer.is_valid():
            username = serializer.validated_data['minecraft_username']
            if not is_valid_minecraft_username(username):
                return Response({'error': 'Invalid Minecraft username'}, status=400)

            purchase = serializer.save()

            # Prepare payment request
            amount = int(purchase.item.price * 10)  # Convert to Rials (multiply by 10)
            description = f"Purchase of {purchase.item.display_name or purchase.item.name}"
            callback_url = request.build_absolute_uri('/api/ipg/callback/')
            mobile = purchase.mobile_number
            order_id = str(purchase.id)

            # Request payment from Zarinpal
            success, response_data = request_payment(
                amount=amount,
                description=description,
                callback_url=callback_url,
                mobile=mobile,
                order_id=order_id
            )

            if success:
                # Save authority to purchase
                purchase.authority = response_data['authority']
                purchase.save()

                # Generate payment URL
                payment_url = get_payment_url(response_data['authority'])

                return Response({
                    'payment_url': payment_url,
                    'reference_id': purchase.ref_id,
                    'authority': response_data['authority']
                })
            else:
                # Payment request failed
                purchase.state = Purchase.State.FAILED
                purchase.save()

                return Response({
                    'error': 'Payment request failed',
                    'details': response_data
                }, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class IPGCallbackView(APIView):
    def get(self, request):
        # Get parameters from Zarinpal callback
        authority = request.query_params.get('Authority')
        status_param = request.query_params.get('Status')

        if not authority:
            # Redirect to frontend with error
            frontend_url = build_frontend_payment_result_url('failed', message='Authority parameter is required')
            return HttpResponseRedirect(frontend_url)

        if not status_param:
            # Redirect to frontend with error
            frontend_url = build_frontend_payment_result_url('failed', message='Status parameter is required')
            return HttpResponseRedirect(frontend_url)

        # Find purchase by authority
        try:
            purchase = Purchase.objects.get(authority=authority)
        except Purchase.DoesNotExist:
            # Redirect to frontend with error
            frontend_url = build_frontend_payment_result_url('failed', message='Purchase not found')
            return HttpResponseRedirect(frontend_url)

        # Check if payment was canceled or failed
        if status_param != 'OK':
            purchase.state = Purchase.State.FAILED
            purchase.save()
            # Redirect to frontend with failure status
            frontend_url = build_frontend_payment_result_url('failed', ref_id=purchase.ref_id,
                                                             message='Payment was cancelled or failed')
            return HttpResponseRedirect(frontend_url)

        # Verify payment with Zarinpal
        amount = int(purchase.item.price * 10)  # Convert to Rials
        success, response_data = verify_payment(authority, amount)
        # save Zarinpal verify response
        purchase.save_zarinpal_verify_response(response_data)

        if not success:
            purchase.state = Purchase.State.FAILED
            purchase.save()
            # Redirect to frontend with verification failure
            frontend_url = build_frontend_payment_result_url('failed', ref_id=purchase.ref_id,
                                                             message='Payment verification failed')
            return HttpResponseRedirect(frontend_url)

        # Check verification code
        # Code 100: First successful verification - process the purchase
        # Code 101: Already verified before - don't process again
        verification_code = response_data.get('code')

        if verification_code == 100:
            # First time verification - process the purchase
            purchase.state = Purchase.State.SUCCESSFUL
            purchase.zarinpal_ref_id = response_data.get('ref_id')
            purchase.payment_succeeded_at = timezone.now()
            purchase.save()

            # Execute the purchase completion logic
            item = purchase.item
            server = item.minecraft_server
            expiration_days = item.expiration_days or 0
            now = timezone.now()

            # Execute Minecraft commands
            if server and item.commands:
                commands = [cmd.strip() for cmd in item.commands.split(',') if cmd.strip()]
                command_success = True

                for command in commands:
                    command = command.replace("{username}", purchase.minecraft_username)
                    if not send_command_to_minecraft_server(server, command):
                        command_success = False
                        break

                purchase.state = Purchase.State.FINISHED if command_success else Purchase.State.COMMAND_FAILED
            else:
                purchase.state = Purchase.State.COMMAND_FAILED

            # Apply subscription logic
            if expiration_days > 0:
                new_expires_at = now + timedelta(days=expiration_days)

                existing = Purchase.objects.filter(
                    minecraft_username=purchase.minecraft_username,
                    item=item,
                    subscription_status=Purchase.SubscriptionStatus.SUBSCRIBED,
                    expires_at__gt=now
                ).order_by('-expires_at').last()

                if existing:
                    # Extend by remaining time
                    remaining = existing.expires_at - now
                    new_expires_at += remaining

                purchase.subscription_status = Purchase.SubscriptionStatus.SUBSCRIBED
                purchase.expires_at = new_expires_at

                # Schedule expiration task
                schedule(
                    'shop.tasks.revoke_item',
                    purchase.id,
                    schedule_type='O',
                    next_run=new_expires_at
                )
            else:
                purchase.subscription_status = Purchase.SubscriptionStatus.ONETIME

            purchase.save()
            # Redirect to frontend with success status
            frontend_url = build_frontend_payment_result_url('success', ref_id=purchase.ref_id,
                                                             message='Payment completed successfully')
            return HttpResponseRedirect(frontend_url)

        elif verification_code == 101:
            # Already verified before - don't process again
            # Just update ref_id if not already set
            if not purchase.zarinpal_ref_id:
                purchase.zarinpal_ref_id = response_data.get('ref_id')
            purchase.save()

            # Redirect to frontend with already processed status
            frontend_url = build_frontend_payment_result_url('success', ref_id=purchase.ref_id,
                                                             message='Payment was already processed')
            return HttpResponseRedirect(frontend_url)

        else:
            # Unexpected verification code
            purchase.state = Purchase.State.FAILED
            purchase.save()
            # Redirect to frontend with unexpected error
            frontend_url = build_frontend_payment_result_url('failed', ref_id=purchase.ref_id,
                                                             message=f'Unexpected verification code: {verification_code}')
            return HttpResponseRedirect(frontend_url)


class ShopAPIView(APIView):
    def get(self, request):
        # Get all published categories
        categories = Category.objects.filter(published=True)
        categories_serializer = CategorySerializer(categories, many=True)

        # Get all published content creators
        content_creators = ContentCreator.objects.filter(published=True)
        creators_serializer = ContentCreatorSerializer(content_creators, many=True)

        # Get all published items where both the Minecraft server and category are published
        items = Item.objects.filter(
            published=True,
            category__published=True,
            minecraft_server__published=True
        ).select_related('category', 'minecraft_server')

        items_serializer = ItemSerializer(items, many=True)

        return Response({
            'categories': categories_serializer.data,
            'content_creators': creators_serializer.data,
            'items': items_serializer.data
        })


class CheckMinecraftUsernameView(APIView):
    def get(self, request):
        username = request.query_params.get('username')

        if not username:
            return Response(
                {'error': 'Username parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        is_registered = is_valid_minecraft_username(username)

        return Response({
            'username': username,
            'is_registered': is_registered,
        })


class HomeAPIView(APIView):
    def get(self, request):
        # Get online player count from the proxy server
        online_players = 0
        try:
            # Find the proxy server
            proxy_server = MinecraftServer.objects.filter(proxy=True, enabled=True).first()

            if proxy_server and proxy_server.ip and proxy_server.port:
                server_address = f"{proxy_server.ip}:{proxy_server.port}"
                server = JavaServer.lookup(server_address)
                query = server.status()
                online_players = query.players.online

        except Exception as e:
            # Log the error but continue with the response
            print(f"Error querying Minecraft server: {e}")

        # Get download links
        download_links = DownloadLink.objects.filter(published=True)
        download_links_serializer = DownloadLinkSerializer(download_links, many=True)

        # Format the response
        download_links_dict = {link['platform']: link['url'] for link in download_links_serializer.data}

        return Response({
            'online_players': online_players,
            'download_links': download_links_dict
        })
