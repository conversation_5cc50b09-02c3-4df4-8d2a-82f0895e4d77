# Django-specific files
*.log
*.pot
*.pyc
__pycache__/
local_settings.py
db.sqlite3
media/
static/

# PyCharm-specific files
.idea/


# Python-related files
*.py[cod]
*$py.class
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
/shelf/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.whl
*.egg-info/
.installed.cfg
*.egg
*.manifest
*.spec

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Unit test / coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
.pytest_cache/
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Jupyter Notebook
.ipynb_checkpoints/

# IDE-specific files
.vscode/
.vscode/*

# Miscellaneous
*.bak
/dbbackups/
