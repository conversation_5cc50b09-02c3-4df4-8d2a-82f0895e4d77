#!/bin/bash
set -e

echo "Setting up MCShop as a combined system service..."

# Make start-all.bash executable
chmod +x start-all.bash

# Copy service file to systemd directory
sudo cp mcshop-combined.service /etc/systemd/system/

# Reload systemd to recognize the new service
sudo systemctl daemon-reload

# Enable the service to start on boot
sudo systemctl enable mcshop-combined

# Start the service
sudo systemctl start mcshop-combined

# Check status
echo "MCShop combined service status:"
sudo systemctl status mcshop-combined --no-pager

echo ""
echo "Combined service setup complete!"
echo ""
echo "Useful commands:"
echo "  sudo systemctl start mcshop-combined     # Start both Django and Django-Q"
echo "  sudo systemctl stop mcshop-combined      # Stop both services"
echo "  sudo systemctl restart mcshop-combined   # Restart both services"
echo "  sudo systemctl status mcshop-combined    # Check service status"
echo "  sudo journalctl -u mcshop-combined -f    # View live logs"
